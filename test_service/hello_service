#!/bin/sh /etc/rc.common

START=99
USE_PROCD=1        # 告诉系统用 procd

SERVICE_PROG="/usr/bin/python3"
SERVICE_ARGS="/home/<USER>/bms_Uploader.py"

start_service() {
    procd_open_instance
    procd_set_param command "$SERVICE_PROG" "$SERVICE_ARGS"
    procd_set_param stdout 1      # 把 stdout / stderr 打进系统日志
    procd_set_param stderr 1
    procd_set_param respawn       # (可选) 崩溃后 5s 自动重启
    procd_close_instance
}