#e_data = [5, 1, 0, 215, 12, 60, 40, 42]
def PackageBMS(dec_data):
    hex_trs = ["0x%02x" % i for i in dec_data]
    hex_joint = [str(hex_trs[0]), str(hex_trs[1]), str(hex_trs[2]), str(hex_trs[3]), str(hex_trs[4]), str(hex_trs[5]), str(hex_trs[6]), str(hex_trs[7])]
    bms_joint = hex_joint[0][2:] + " " + hex_joint[1][2:] + " " + hex_joint[2][2:] + " " + hex_joint[3][2:] + " " + hex_joint[4][2:] + " " + hex_joint[5][2:] + " " + hex_joint[6][2:] + " " + hex_joint[7][2:]
    #print(bms_joint)
    return bms_joint


#packageBMS(e_data)