#!/bin/sh /etc/rc.common

START=43

USE_PROCD=1
PROG=/usr/bin/python3

start_service() {
        [ -e "/data/can/TaskDict.py" ] && {
                procd_open_instance
                procd_set_param command "$PROG" "/data/can/TaskDict.py"
                procd_set_param env PYTHONPATH=/data/pythonpackages
                procd_set_param respawn 3600 10 0
                procd_close_instance
        }
}