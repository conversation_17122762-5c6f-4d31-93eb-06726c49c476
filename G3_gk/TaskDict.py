#!/usr/bin/python3
# !/usr/bin/env python
from CanUtils import *
from log import *
import can
from PackageBMS import *
from BmsDict import *
from PostProtocolData import *
from wholeVariable import *
from datetime import *
import json
import time

if __name__ == "__main__":

    can_setup();
    while True:
        time13 = int(round(time.time() * 1000))
        try:
            bms = {}
            bus = can.interface.Bus()
            enter_time = datetime.now()
            while True:
              #  time.sleep(0.05);
                msg = bus.recv(80000)
                judge_time = datetime.now()
                if (judge_time - enter_time > timedelta(seconds=bmsperiod)):
                    break
                else:
                    bms.update({"vin": vin})
                    bms.update({"time": time13})
                    if msg.arbitration_id in bms_id_dict:
                        BMSData = PackageBMS(msg.data)
                        bms.update({str(msg.arbitration_id): str(BMSData)})
            format_bms = json.dumps(bms)
            PushProtocolData(url, format_bms)

        except can.CanError:
            print("Message NOT sent")
            log = Logger('.log', level='debug')
            log.logger.critical('程序崩溃')

    can_stop()
