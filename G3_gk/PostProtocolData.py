#!/usr/bin/env python
import logging

import requests


def PushProtocolData(url, protocalData):
    logging.basicConfig(level=logging.DEBUG)
    print(f"ProTocalData: {protocalData}")
   # print(url)
    headers = {'Content-Type': 'application/json'}
    r = requests.post(url=url, headers=headers, data=protocalData, timeout=5)  # 发送POST请求
    print(f"url: {url}, ProTocalData:{protocalData}")
    print(r.status_code)
#
# if __name__ == "__main__":
#     url = 'https://gw-pre.am.xiaojukeji.com/quick-link/cmd/yijianeng/recvCarConnectStatus'
#     protocalData = '{"vin": "9914000000009997", "time": 1709603433423, "416220124": "08 00 00 00 00 10 27 e0"}'
#     PushProtocolData(url, protocalData)