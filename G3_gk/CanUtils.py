import os
import can
import time

'''  加载python-can 模块 '''
can.rc['interface'] = 'socketcan'
can.rc['channel'] = 'can0'
can.rc['bitrate'] = 250000

'''  can 的启动函数 '''
# def can_setup(can_name):
def can_setup():
    '''
    can_setup_command = "canconfig " + can_name + " bitrate 1000000 restart-ms 1000 ctrlmode triple-sampling on";
    can_start_command = "canconfig " + can_name + " start";
    pass;
    os.system(can_setup_command);
    os.system(can_start_command);
    '''
    os.system('ip link set can0 type can bitrate 250000')
    os.system('ifconfig can0 up')

'''  can 停止函数  '''
def can_stop():
    # can_stop_command = "canconfig " + can_name + " stop";
    # os.system(can_stop_command);
    os.system('ifconfig can0 down')

'''   发送信息 '''
def send_one():
    bus = can.interface.Bus();
    msg = can.Message(arbitration_id=0x7f, data=[11, 25, 11, 1, 1, 2, 23, 18], extended_id=False);

    try:
        bus.send(msg);
        print("Message sent on {}".format(bus.channel_info));

    # except can.CanError:
    except:
        print("Message NOT sent");

'''   接收信息   '''
def recv():
    bus = can.interface.Bus();
    msg = bus.recv(100);
    #接收信息
    try:
        #bus.send(msg);
        if msg is None:
            print('No message was received')
        else:
            print(msg.data[0]);  # 接收回来的第一个字节的数据
            return msg;

    except can.CanError:
        print("Message NOT sent");