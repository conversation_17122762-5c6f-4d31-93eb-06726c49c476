# CAN Logger Simple - 使用说明

## 概述
这是一个不依赖yaml和systemctl的CAN数据采集器，适用于无法安装yaml库或使用systemd的Linux系统。

## 文件说明

### 核心文件
- `config.py` - 配置文件（Python格式）
- `can_logger_simple.py` - 主程序
- `start_can_logger_simple.sh` - 启动脚本

### 配置文件
- `can_config_simple.conf` - 备用的文本格式配置（已被config.py替代）

## 快速开始

### 1. 检查配置
```bash
# 查看当前配置
python3 config.py
```

### 2. 修改配置
编辑 `config.py` 文件，修改以下参数：
- `VIN` - 车辆识别码
- `UPLOAD_URL` - 数据上传地址
- `CAN_INTERFACE` - CAN接口名称（默认can0）
- `CAN_ID_LIST` - 需要监听的CAN ID列表

### 3. 启动服务
```bash
# 给启动脚本添加执行权限
chmod +x start_can_logger_simple.sh

# 启动服务
./start_can_logger_simple.sh start
```

## 启动脚本使用方法

### 基本命令
```bash
./start_can_logger_simple.sh {start|stop|restart|status|logs}
```

### 命令详解

#### 启动服务
```bash
./start_can_logger_simple.sh start
```
- 检查是否已运行，如果未运行则启动
- 在后台运行，生成PID文件
- 输出重定向到日志文件

#### 停止服务
```bash
./start_can_logger_simple.sh stop
```
- 优雅停止服务（发送TERM信号）
- 如果10秒内未停止，强制杀死进程
- 清理PID文件

#### 重启服务
```bash
./start_can_logger_simple.sh restart
```
- 先停止再启动服务

#### 查看状态
```bash
./start_can_logger_simple.sh status
```
- 显示服务运行状态
- 显示进程信息和运行时间
- 显示最近10行日志

#### 查看日志
```bash
# 查看最近50行日志
./start_can_logger_simple.sh logs

# 实时查看日志
./start_can_logger_simple.sh logs -f
```

## 后台运行特性

### 持久化运行
- ✅ **支持后台运行** - 使用nohup确保进程在终端关闭后继续运行
- ✅ **PID管理** - 自动生成和管理PID文件
- ✅ **日志记录** - 所有输出重定向到日志文件
- ✅ **优雅停止** - 支持SIGTERM信号优雅停止
- ✅ **状态监控** - 可随时检查运行状态

### 自动重启
如果需要开机自启动，可以添加到crontab：
```bash
# 编辑crontab
crontab -e

# 添加以下行（替换为实际路径）
@reboot /path/to/start_can_logger_simple.sh start
```

### 监控和维护
```bash
# 检查是否在运行
./start_can_logger_simple.sh status

# 查看实时日志
./start_can_logger_simple.sh logs -f

# 如果出现问题，重启服务
./start_can_logger_simple.sh restart
```

## 文件位置

### 运行时文件
- `can_logger.pid` - 进程ID文件
- `can_logger_output.log` - 程序输出日志
- `can_error.log` - 错误日志
- `can_data_backup.json` - 数据备份文件（如果启用）

### 日志管理
程序会生成两种日志：
1. **输出日志** (`can_logger_output.log`) - 程序的标准输出
2. **错误日志** (`can_error.log`) - 上传失败等错误信息

## 故障排除

### 常见问题

#### 1. 启动失败
```bash
# 检查配置
python3 config.py

# 查看详细错误
./start_can_logger_simple.sh logs
```

#### 2. CAN接口问题
```bash
# 检查CAN接口状态
ip link show can0

# 手动配置CAN接口
sudo ip link set can0 type can bitrate 250000
sudo ifconfig can0 up
```

#### 3. 权限问题
```bash
# 确保脚本有执行权限
chmod +x start_can_logger_simple.sh

# 确保有CAN接口操作权限（可能需要sudo）
```

#### 4. 进程僵死
```bash
# 强制停止
./start_can_logger_simple.sh stop

# 如果还是无法停止，手动杀死
pkill -f can_logger_simple.py
```

## 配置示例

### 修改VIN码
```python
# 在config.py中修改
VIN = "YOUR_VEHICLE_VIN"
```

### 修改上传地址
```python
# 在config.py中修改
UPLOAD_URL = "http://your-server.com/api/data"
```

### 添加新的CAN ID
```python
# 在config.py的CAN_ID_LIST中添加
CAN_ID_LIST = [
    218070019,
    218070275,
    # 添加新的ID
    123456789,
]
```

## 性能说明
- 内存占用：约10-20MB
- CPU占用：正常情况下<1%
- 网络：每10秒上传一次数据
- 磁盘：日志文件会持续增长，建议定期清理
