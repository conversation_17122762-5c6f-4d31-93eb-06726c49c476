#!/bin/sh

SCRIPT_DIR="/home/<USER>"

# === 拷贝文件 ===
#sudo mkdir -p $SCRIPT_DIR
#sudo cp can_logger_with_config.py can_config.yaml clean_log.sh $SCRIPT_DIR

# === 设置权限 ===
chown -R root:rigoiot $SCRIPT_DIR
chmod -R 755 $SCRIPT_DIR

# === 预创建日志文件 ===
touch $SCRIPT_DIR/can_error.log
touch $SCRIPT_DIR/can_data_backup.json
chown root:rigoiot $SCRIPT_DIR/*.log $SCRIPT_DIR/*.json 2>/dev/null || true
chmod 664 $SCRIPT_DIR/*.log $SCRIPT_DIR/*.json 2>/dev/null || true

# === 安装依赖 ===
pip3 install python-can requests pyyaml

# === 安装服务文件 ===
cp can-logger.service /etc/systemd/system/can-logger.service
chmod +x $SCRIPT_DIR/*.sh

# === 注册定时清理任务 ===
echo "[+] 添加日志清理定时任务..."
(crontab -l 2>/dev/null; echo "0 */6 * * * bash $SCRIPT_DIR/clean_log.sh") | crontab -

# === 启动服务 ===
systemctl daemon-reexec
systemctl daemon-reload
systemctl enable can-logger
systemctl restart can-logger

echo "[✓] 安装完成。使用 'journalctl -fu can-logger' 查看日志"