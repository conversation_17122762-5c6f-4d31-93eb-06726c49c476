#!/bin/sh

# 部署和测试 bms_can_service 脚本

echo "=== BMS Uploader 服务部署和测试脚本 ==="

# 定义路径
SERVICE_NAME="bms_can_service"
SERVICE_SCRIPT="G3/bms_can_service"
TARGET_DIR="/home/<USER>"
INIT_SCRIPT="/etc/init.d/$SERVICE_NAME"

echo "[1] 检查必要文件..."

# 检查源文件是否存在
if [ ! -f "$SERVICE_SCRIPT" ]; then
    echo "[✗] 错误: $SERVICE_SCRIPT 不存在"
    exit 1
fi

if [ ! -f "G3/bms_Uploader.py" ]; then
    echo "[✗] 错误: G3/bms_Uploader.py 不存在"
    exit 1
fi

if [ ! -f "G3/config.py" ]; then
    echo "[✗] 错误: G3/config.py 不存在"
    exit 1
fi

echo "[✓] 所有源文件存在"

echo "[2] 创建目标目录..."
mkdir -p "$TARGET_DIR"
echo "[✓] 目标目录已创建: $TARGET_DIR"

echo "[3] 复制文件..."
cp "G3/bms_Uploader.py" "$TARGET_DIR/"
cp "G3/config.py" "$TARGET_DIR/"
cp "G3/test_import.py" "$TARGET_DIR/" 2>/dev/null || echo "[i] test_import.py 不存在，跳过"

echo "[✓] Python 文件已复制"

echo "[4] 测试 Python 导入..."
cd "$TARGET_DIR"
python3 -c "
import sys
sys.path.insert(0, '$TARGET_DIR')
try:
    from config import *
    print('[✓] 配置文件导入成功')
    print(f'[INFO] CAN接口: {CAN_INTERFACE}')
    print(f'[INFO] VIN: {VIN}')
    print(f'[INFO] CAN ID数量: {len(CAN_ID_LIST)}')
except Exception as e:
    print(f'[✗] 配置文件导入失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "[✗] Python 导入测试失败"
    exit 1
fi

echo "[5] 停止现有服务..."
if [ -f "$INIT_SCRIPT" ]; then
    "$INIT_SCRIPT" stop 2>/dev/null
    "$INIT_SCRIPT" disable 2>/dev/null
    echo "[✓] 已停止现有服务"
fi

echo "[6] 安装新的服务脚本..."
cp "$SERVICE_SCRIPT" "$INIT_SCRIPT"
chmod +x "$INIT_SCRIPT"
echo "[✓] 服务脚本已安装: $INIT_SCRIPT"

echo "[7] 启用并启动服务..."
"$INIT_SCRIPT" enable
"$INIT_SCRIPT" start

if [ $? -eq 0 ]; then
    echo "[✓] 服务启动成功"
else
    echo "[✗] 服务启动失败"
    exit 1
fi

echo "[8] 等待服务稳定..."
sleep 5

echo "[9] 检查服务状态..."
logread | tail -20 | grep -i bms

echo ""
echo "=== 部署完成 ==="
echo "服务名称: $SERVICE_NAME"
echo "检查日志: logread | grep bms"
echo "重启服务: /etc/init.d/$SERVICE_NAME restart"
echo "停止服务: /etc/init.d/$SERVICE_NAME stop"
echo "查看状态: /etc/init.d/$SERVICE_NAME status"
