#!/bin/sh /etc/rc.common

START=99
USE_PROCD=1

start_service() {
    procd_open_instance
    procd_set_param command /usr/bin/python3 /home/<USER>/bms_Uploader.py
    procd_set_param stdout 1
    procd_set_param stderr 1
    # procd_set_param respawn
    procd_close_instance
}

# SERVICE_DIR="/home/<USER>"
# PYTHON="/usr/bin/python3"
# SCRIPT="$SERVICE_DIR/bms_Uploader.py"
# LOG="$SERVICE_DIR/output.log"
# ERR="$SERVICE_DIR/uploader_error.log"

# start_service() {
#     # 初始化环境
#     # if [ -x "$SERVICE_DIR/init_env.sh" ]; then
#     #     "$SERVICE_DIR/init_env.sh"
#     # fi

#     procd_open_instance
#     procd_set_param command "$PYTHON" "$SCRIPT"
#     procd_set_param stdout "$LOG"
#     procd_set_param stderr "$ERR"
#     procd_set_param respawn
#     procd_close_instance
# }