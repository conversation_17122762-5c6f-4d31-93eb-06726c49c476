#!/bin/sh

SCRIPT_DIR="/home/<USER>"

# === 拷贝文件 ===
# echo "[+] 拷贝文件到 $SCRIPT_DIR ..."
# sudo mkdir -p $SCRIPT_DIR
# sudo cp config.py can_logger_simple.py clean_log.sh $SCRIPT_DIR

# === 设置权限 ===
echo "[+] 设置文件权限..."
chown -R root:rigoiot $SCRIPT_DIR
chmod -R 755 $SCRIPT_DIR

# === 预创建日志文件 ===
echo "[+] 创建日志文件..."
touch $SCRIPT_DIR/can_error.log
touch $SCRIPT_DIR/can_data_backup.json
touch $SCRIPT_DIR/can_logger.pid
touch $SCRIPT_DIR/can_logger_output.log
chown root:rigoiot $SCRIPT_DIR/*.log $SCRIPT_DIR/*.json $SCRIPT_DIR/*.pid 2>/dev/null || true
chmod 664 $SCRIPT_DIR/*.log $SCRIPT_DIR/*.json $SCRIPT_DIR/*.pid 2>/dev/null || true

# === 安装依赖 ===
# echo "[+] 安装Python依赖..."
# pip3 install python-can requests

# === 注册定时清理任务 ===
echo "[+] 添加日志清理定时任务..."
(crontab -l 2>/dev/null; echo "0 */6 * * * bash $SCRIPT_DIR/clean_log.sh") | crontab -

# === 停止可能存在的进程 ===
echo "[+] 停止现有进程..."
if [ -f "$SCRIPT_DIR/can_logger.pid" ]; then
    PID=$(cat $SCRIPT_DIR/can_logger.pid)
    if ps -p "$PID" > /dev/null 2>&1; then
        kill -TERM "$PID" 2>/dev/null
        sleep 2
        if ps -p "$PID" > /dev/null 2>&1; then
            kill -KILL "$PID" 2>/dev/null
        fi
    fi
    rm -f $SCRIPT_DIR/can_logger.pid
fi

# === 启动服务 ===
echo "[+] 启动CAN Logger..."
cd $SCRIPT_DIR
nohup python3 can_logger_simple.py > can_logger_output.log 2>&1 &
PID=$!
echo $PID > can_logger.pid

# 检查启动是否成功
sleep 2
if ps -p "$PID" > /dev/null 2>&1; then
    echo "[✓] CAN Logger 启动成功 (PID: $PID)"
    echo "[✓] 日志文件: $SCRIPT_DIR/can_logger_output.log"
    echo "[✓] 错误日志: $SCRIPT_DIR/can_error.log"
    echo ""
    echo "使用以下命令管理服务:"
    echo "  查看状态: ps -p $PID"
    echo "  查看日志: tail -f $SCRIPT_DIR/can_logger_output.log"
    echo "  停止服务: kill $PID"
else
    echo "[!] CAN Logger 启动失败，请检查日志"
    rm -f $SCRIPT_DIR/can_logger.pid
fi
