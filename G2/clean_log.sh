#!/bin/bash

# CAN Logger 日志清理脚本
SCRIPT_DIR="/home/<USER>/can_uploader"
LOG_FILE="$SCRIPT_DIR/can_data_backup.json"
ERROR_LOG="$SCRIPT_DIR/can_error.log"
MAX_SIZE=1048576  # 1MB

# 记录清理操作到系统日志
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    logger -t "can-logger-cleanup" "$1"
}

log_message "开始日志清理检查..."

# 清理数据日志文件
if [ -f "$LOG_FILE" ]; then
    size=$(stat -c%s "$LOG_FILE")
    log_message "数据日志文件大小: ${size} 字节"
    if [ $size -gt $MAX_SIZE ]; then
        echo "{}" > "$LOG_FILE"
        log_message "清空数据日志文件: $LOG_FILE (原大小: ${size} 字节)"
    else
        log_message "数据日志文件大小正常，无需清理"
    fi
else
    log_message "数据日志文件不存在: $LOG_FILE"
fi

# 检查错误日志文件大小（可选清理）
if [ -f "$ERROR_LOG" ]; then
    error_size=$(stat -c%s "$ERROR_LOG")
    log_message "错误日志文件大小: ${error_size} 字节"
    # 如果错误日志超过5MB，保留最后1000行
    if [ $error_size -gt $MAX_SIZE ]; then
        tail -n 1000 "$ERROR_LOG" > "${ERROR_LOG}.tmp"
        mv "${ERROR_LOG}.tmp" "$ERROR_LOG"
        log_message "清理错误日志文件: $ERROR_LOG (保留最后1000行)"
    fi
fi

log_message "日志清理检查完成"
