#!/bin/bash

# CAN Logger 增强版日志清理脚本
# 清理多个日志文件，防止磁盘空间不足

SCRIPT_DIR="/home/<USER>/can_uploader"
DATA_LOG_FILE="$SCRIPT_DIR/can_data_log_new.json"
SERVICE_LOG_FILE="$SCRIPT_DIR/can_service.log"
ERROR_LOG_FILE="$SCRIPT_DIR/can_error.log"

# 文件大小限制
DATA_LOG_MAX_SIZE=1048576    # 1MB - 数据日志
SERVICE_LOG_MAX_SIZE=5242880 # 5MB - 服务日志
ERROR_LOG_MAX_SIZE=2097152   # 2MB - 错误日志

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 清理数据日志文件
clean_data_log() {
    if [ -f "$DATA_LOG_FILE" ]; then
        size=$(stat -c%s "$DATA_LOG_FILE" 2>/dev/null || echo 0)
        if [ $size -gt $DATA_LOG_MAX_SIZE ]; then
            echo "{}" > "$DATA_LOG_FILE"
            log_message "清空数据日志文件: $DATA_LOG_FILE (原大小: ${size} 字节)"
            return 0
        else
            log_message "数据日志文件大小正常: $DATA_LOG_FILE (${size} 字节)"
        fi
    else
        log_message "数据日志文件不存在: $DATA_LOG_FILE"
    fi
    return 1
}

# 清理服务日志文件（保留最后1000行）
clean_service_log() {
    if [ -f "$SERVICE_LOG_FILE" ]; then
        size=$(stat -c%s "$SERVICE_LOG_FILE" 2>/dev/null || echo 0)
        if [ $size -gt $SERVICE_LOG_MAX_SIZE ]; then
            # 保留最后1000行
            tail -n 1000 "$SERVICE_LOG_FILE" > "${SERVICE_LOG_FILE}.tmp"
            mv "${SERVICE_LOG_FILE}.tmp" "$SERVICE_LOG_FILE"
            log_message "清理服务日志文件: $SERVICE_LOG_FILE (原大小: ${size} 字节，保留最后1000行)"
            return 0
        else
            log_message "服务日志文件大小正常: $SERVICE_LOG_FILE (${size} 字节)"
        fi
    else
        log_message "服务日志文件不存在: $SERVICE_LOG_FILE"
    fi
    return 1
}

# 清理错误日志文件（保留最后500行）
clean_error_log() {
    if [ -f "$ERROR_LOG_FILE" ]; then
        size=$(stat -c%s "$ERROR_LOG_FILE" 2>/dev/null || echo 0)
        if [ $size -gt $ERROR_LOG_MAX_SIZE ]; then
            # 保留最后500行
            tail -n 500 "$ERROR_LOG_FILE" > "${ERROR_LOG_FILE}.tmp"
            mv "${ERROR_LOG_FILE}.tmp" "$ERROR_LOG_FILE"
            log_message "清理错误日志文件: $ERROR_LOG_FILE (原大小: ${size} 字节，保留最后500行)"
            return 0
        else
            log_message "错误日志文件大小正常: $ERROR_LOG_FILE (${size} 字节)"
        fi
    else
        log_message "错误日志文件不存在: $ERROR_LOG_FILE"
    fi
    return 1
}

# 主清理流程
main() {
    log_message "开始日志清理检查..."
    
    cleaned_count=0
    
    # 清理各个日志文件
    if clean_data_log; then
        ((cleaned_count++))
    fi
    
    if clean_service_log; then
        ((cleaned_count++))
    fi
    
    if clean_error_log; then
        ((cleaned_count++))
    fi
    
    if [ $cleaned_count -gt 0 ]; then
        log_message "日志清理完成，共清理了 $cleaned_count 个文件"
    else
        log_message "所有日志文件大小正常，无需清理"
    fi
    
    # 显示磁盘使用情况
    if command -v df >/dev/null 2>&1; then
        disk_usage=$(df -h "$SCRIPT_DIR" | tail -1 | awk '{print $5}')
        log_message "当前磁盘使用率: $disk_usage"
    fi
}

# 执行主函数
main
