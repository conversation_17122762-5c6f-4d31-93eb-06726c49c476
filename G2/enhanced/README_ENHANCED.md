# CAN Logger 增强版说明文档

## 概述

本项目提供了两个版本的 CAN Logger：
- **原版本**: `can_logger_with_config.py` - 基础功能版本
- **增强版**: `can_logger_enhanced.py` - 带完整日志记录功能的版本

## 版本对比

### 原版本 (can_logger_with_config.py)
- ✅ 基本的 CAN 数据记录和上传功能
- ✅ 简单的错误日志记录
- ❌ 缺少启动/退出日志
- ❌ 无优雅关闭机制
- ❌ 无详细的运行状态记录

### 增强版 (can_logger_enhanced.py)
- ✅ 完整的双日志系统
- ✅ 详细的启动和退出日志记录
- ✅ 优雅的信号处理和关闭机制
- ✅ 线程安全的退出处理
- ✅ 运行状态监控和统计
- ✅ 更好的错误处理和日志记录

## 新增功能详解

### 1. 双日志系统
```
can_service.log  - 服务日志（启动、停止、状态变化）
can_error.log    - 错误日志（异常和错误信息）
```

### 2. 详细的启动日志
```
2024-07-04 10:30:15,123 - INFO - ==================================================
2024-07-04 10:30:15,124 - INFO - CAN Logger 增强版服务启动
2024-07-04 10:30:15,125 - INFO - ==================================================
2024-07-04 10:30:15,126 - INFO - 启动时间: 2024-07-04 10:30:15
2024-07-04 10:30:15,127 - INFO - 配置文件: can_config.yaml
2024-07-04 10:30:15,128 - INFO - CAN接口: can0
2024-07-04 10:30:15,129 - INFO - 波特率: 250000
```

### 3. 优雅退出机制
- 捕获 SIGTERM 和 SIGINT 信号
- 等待上传线程正常结束
- 关闭 CAN 总线连接
- 记录完整的关闭流程

### 4. 运行状态监控
- 上传次数统计
- 线程状态监控
- 磁盘使用情况检查

## 安装和使用

### 安装增强版
```bash
# 给安装脚本执行权限
chmod +x install_can_logger_enhanced.sh

# 运行安装脚本
./install_can_logger_enhanced.sh
```

### 常用命令
```bash
# 查看服务状态
sudo systemctl status can-logger-enhanced

# 查看实时系统日志
journalctl -fu can-logger-enhanced

# 查看服务日志
tail -f /home/<USER>/can_uploader/can_service.log

# 查看错误日志
tail -f /home/<USER>/can_uploader/can_error.log

# 重启服务
sudo systemctl restart can-logger-enhanced
```

## 日志文件说明

### 服务日志 (can_service.log)
记录内容：
- 服务启动和停止时间
- 配置参数信息
- CAN 接口状态
- 线程启动和停止
- 上传统计信息
- 信号处理事件

### 错误日志 (can_error.log)
记录内容：
- 上传失败错误
- CAN 接口异常
- 配置文件错误
- 网络连接问题

## 日志清理

增强版提供了更智能的日志清理机制：

### 清理策略
- **数据日志**: 超过 1MB 时清空
- **服务日志**: 超过 5MB 时保留最后 1000 行
- **错误日志**: 超过 2MB 时保留最后 500 行

### 手动清理
```bash
# 运行增强版清理脚本
bash clean_log_enhanced.sh
```

## 迁移指南

### 从原版本迁移到增强版

1. **停止原服务**
```bash
sudo systemctl stop can-logger
```

2. **安装增强版**
```bash
./install_can_logger_enhanced.sh
```

3. **验证运行**
```bash
sudo systemctl status can-logger-enhanced
tail -f /home/<USER>/can_uploader/can_service.log
```

### 同时运行两个版本
如果需要同时运行，请确保：
- 使用不同的 CAN 接口
- 使用不同的日志文件路径
- 使用不同的服务名称

## 故障排除

### 常见问题

1. **服务启动失败**
```bash
# 查看详细错误信息
journalctl -u can-logger-enhanced --since "5 minutes ago"
```

2. **日志文件权限问题**
```bash
# 修复权限
sudo chown ubuntu:ubuntu /home/<USER>/can_uploader/*.log
```

3. **CAN 接口问题**
```bash
# 检查 CAN 接口状态
ip link show can0
```

## 性能对比

| 功能 | 原版本 | 增强版 |
|------|--------|--------|
| 内存使用 | 低 | 稍高 |
| CPU 使用 | 低 | 稍高 |
| 磁盘 I/O | 低 | 中等 |
| 可维护性 | 低 | 高 |
| 故障诊断 | 困难 | 容易 |

## 建议

- **生产环境**: 推荐使用增强版，便于监控和故障排除
- **测试环境**: 可以使用原版本，资源消耗更低
- **调试阶段**: 强烈推荐增强版，日志信息更详细

## 技术支持

如有问题，请查看：
1. 服务日志: `can_service.log`
2. 错误日志: `can_error.log`
3. 系统日志: `journalctl -u can-logger-enhanced`
