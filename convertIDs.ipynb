import yaml
import pandas as pd


excel_file_path = "/Users/<USER>/Desktop/协议文件/自研桩国标桩协议/G3-CAN_BMS2DEX.xlsx"
yaml_file_path = "can_ids.yaml"

df = pd.read_excel(excel_file_path)

can_id_list = df['Dec'].dropna().astype(int).tolist()
yaml_data = {'can_id_list': can_id_list}

# # 写入 YAML 文件
# with open(yaml_file_path, 'w') as file:
#     yaml.dump(yaml_data, file, sort_keys=False, default_flow_style=False)

yaml_data['can_id_list'] # 73 values

bms_id_dict = [416220124, 404544515, 415947779, 416013315, 405789699, 405724163, 218070019, 403954691, 403889155,
               403823619, 404478979, 404413443, 405658627, 405593091, 405527555, 405462019, 405396483,
               405330947, 405265411, 405199875, 405134339, 405068803, 405003267, 404937731, 404872195, 404806659,
               404741123, 404675587, 404610051, 403758083, 403692547, 403627011, 403561475, 403495939, 403430403,
               403364867, 403299331, 403233795, 403168259, 403102723, 403037187, 402971651, 402906115, 402840579,
               402775043, 402709507, 404347907, 404282371, 404216835, 404151299, 404085763, 404020227, 415882243,
               218070275, 218071299, 218071043, 416537603, 416603139, 422, 550, 416209923, 416144387, 416078851,
               218071555, 218070787, 218070531,
               406051843,
			   415292419,
			   415357955,
			   415423491,
			   415489027,
			   415554563,
			   415620099,
			   415685635,
			   415751171,
			   415816707,
			   415882243,
			   25996291,
			   416013315,
			   26008579,
			   416340995,
			   416406531,
			   417061891,
			   218071811,
               405855235,
			   405920771,
			   405986307
] # 87 values, 85 in fact

type(bms_id_dict)

def compare_lists(yaml_list, py_list):
    only_in_yaml = set(yaml_list) - set(py_list)
    only_in_py = set(py_list) - set(yaml_list)
    return only_in_yaml, only_in_py


    only_in_yaml, only_in_py = compare_lists(yaml_data['can_id_list'], bms_id_dict)

    print(f"✅ YAML only: {only_in_yaml}")
    print(f"✅ Python only: {only_in_py}")

len(only_in_py)

416340995：0x18D0DC03 #BMS发送总电压用于排查继电器故障
416406531：0x18D1DC03 #BMS发送极限保护的脱钩请求指令和故障代码
416537603：0x18D3DC03 #BMS发送 BMS 当前工作模式，与 VCU模式对应
416603139：0x18D4DC03 #BMS补电数据协议
550：0x226 #车辆底盘自动模式速度数据
422：0x1A6 #车辆底盘故障数据


416209923：0x18CEDC03
218071811：0xCFF8303
416144387：0x18CDDC03
25996291：0x18CAC03
417061891：0x18DBDC03
26008579：0x18CDC03
